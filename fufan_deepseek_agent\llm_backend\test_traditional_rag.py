#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
传统RAG系统测试 - 基于FAISS + SentenceTransformers
"""

import asyncio
import sys
import os
import json
import tempfile
from pathlib import Path
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.embedding_service import EmbeddingService
from app.core.logger import get_logger

logger = get_logger(service="traditional_rag_test")

def create_test_pdf():
    """创建测试PDF文件"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # 创建临时PDF文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_path = temp_file.name
        temp_file.close()
        
        # 创建PDF内容
        c = canvas.Canvas(temp_path, pagesize=letter)
        
        # 第一页
        c.drawString(100, 750, "智能家居产品介绍")
        c.drawString(100, 720, "")
        c.drawString(100, 690, "1. 智能音箱")
        c.drawString(120, 660, "- 小米智能音箱Pro：支持语音控制，价格299元")
        c.drawString(120, 630, "- 天猫精灵X1：AI语音助手，价格199元")
        c.drawString(100, 600, "")
        c.drawString(100, 570, "2. 智能灯具")
        c.drawString(120, 540, "- 飞利浦智能灯泡：可调色温，价格89元")
        c.drawString(120, 510, "- 小米智能台灯：护眼设计，价格169元")
        c.showPage()
        
        # 第二页
        c.drawString(100, 750, "智能安防产品")
        c.drawString(100, 720, "")
        c.drawString(100, 690, "1. 智能摄像头")
        c.drawString(120, 660, "- 海康威视摄像头：1080P高清，价格399元")
        c.drawString(120, 630, "- 小米摄像头：夜视功能，价格149元")
        c.drawString(100, 600, "")
        c.drawString(100, 570, "2. 智能门锁")
        c.drawString(120, 540, "- 德施曼智能锁：指纹识别，价格1299元")
        c.drawString(120, 510, "- 凯迪仕智能锁：密码+指纹，价格899元")
        c.showPage()
        
        c.save()
        
        print(f"✓ 测试PDF文件创建成功: {temp_path}")
        return temp_path
        
    except ImportError:
        print("⚠️  reportlab未安装，使用文本文件代替PDF")
        return create_test_txt()
    except Exception as e:
        print(f"✗ 创建PDF失败: {e}")
        return create_test_txt()

def create_test_txt():
    """创建测试文本文件"""
    try:
        # 创建临时文本文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w', encoding='utf-8')
        
        content = """智能家居产品介绍

1. 智能音箱
- 小米智能音箱Pro：支持语音控制，价格299元
- 天猫精灵X1：AI语音助手，价格199元

2. 智能灯具
- 飞利浦智能灯泡：可调色温，价格89元
- 小米智能台灯：护眼设计，价格169元

智能安防产品

1. 智能摄像头
- 海康威视摄像头：1080P高清，价格399元
- 小米摄像头：夜视功能，价格149元

2. 智能门锁
- 德施曼智能锁：指纹识别，价格1299元
- 凯迪仕智能锁：密码+指纹，价格899元
"""
        
        temp_file.write(content)
        temp_path = temp_file.name
        temp_file.close()
        
        print(f"✓ 测试文本文件创建成功: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"✗ 创建文本文件失败: {e}")
        return None

async def test_embedding_service_basic():
    """测试EmbeddingService基础功能"""
    print("=" * 60)
    print("测试EmbeddingService基础功能")
    print("=" * 60)
    
    try:
        # 创建服务实例
        embedding_service = EmbeddingService()
        print("✓ EmbeddingService实例创建成功")
        
        # 检查模型加载
        print(f"✓ 模型维度: {embedding_service.dimension}")
        print(f"✓ 索引目录: {embedding_service.index_dir}")
        
        # 测试向量生成
        test_text = "这是一个测试文本"
        vectors = embedding_service.model.encode([test_text])
        print(f"✓ 向量生成成功，维度: {vectors.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ EmbeddingService基础测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pdf_processing():
    """测试PDF文件处理"""
    print("\n" + "=" * 60)
    print("测试PDF/文档处理")
    print("=" * 60)
    
    try:
        # 创建测试文件
        test_file_path = create_test_pdf()
        if not test_file_path:
            return False
        
        # 创建服务实例
        embedding_service = EmbeddingService()
        
        # 处理文件
        if test_file_path.endswith('.pdf'):
            print("处理PDF文件...")
            result = await embedding_service.create_embeddings(test_file_path, "test_index")
        else:
            print("处理文本文件...")
            # 对于文本文件，我们需要修改处理逻辑
            with open(test_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单分块
            chunks = content.split('\n\n')
            chunks = [chunk.strip() for chunk in chunks if chunk.strip()]
            
            # 创建索引
            index = embedding_service._create_index()
            vectors = embedding_service.model.encode(chunks)
            vectors = vectors.astype('float32')
            index.add(vectors)
            
            # 创建文档数据
            documents = {}
            for i, text in enumerate(chunks):
                documents[str(i)] = {
                    "text": text,
                    "metadata": {
                        "chunk": i + 1,
                        "source": test_file_path
                    }
                }
            
            # 保存索引
            import hashlib
            file_hash = hashlib.md5(test_file_path.encode()).hexdigest()
            embedding_service._save_index(file_hash, index, documents)
            
            result = {
                "status": "success",
                "index_id": f"index_{file_hash}",
                "chunks": len(chunks)
            }
        
        print(f"✓ 文件处理成功: {result}")
        
        # 清理临时文件
        os.unlink(test_file_path)
        
        return result
        
    except Exception as e:
        print(f"✗ 文件处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_vector_search():
    """测试向量搜索功能"""
    print("\n" + "=" * 60)
    print("测试向量搜索功能")
    print("=" * 60)
    
    try:
        # 先创建索引
        result = await test_pdf_processing()
        if not result:
            print("✗ 无法创建测试索引")
            return False
        
        index_id = result["index_id"]
        print(f"使用索引: {index_id}")
        
        # 创建服务实例并加载索引
        embedding_service = EmbeddingService()
        await embedding_service.load_index(index_id)
        print("✓ 索引加载成功")
        
        # 测试搜索
        test_queries = [
            "智能音箱的价格",
            "小米产品有哪些",
            "智能门锁功能",
            "摄像头的特点"
        ]
        
        for query in test_queries:
            print(f"\n查询: {query}")
            results = await embedding_service.search(query, top_k=3)
            
            if results:
                print(f"找到 {len(results)} 个相关结果:")
                for i, result in enumerate(results, 1):
                    print(f"  {i}. 相似度: {result['score']:.4f}")
                    print(f"     内容: {result['content'][:100]}...")
                    print(f"     来源: {result['metadata']}")
            else:
                print("未找到相关结果")
        
        return True
        
    except Exception as e:
        print(f"✗ 向量搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_traditional_rag_chat():
    """测试传统RAG问答功能"""
    print("\n" + "=" * 60)
    print("测试传统RAG问答功能")
    print("=" * 60)
    
    try:
        # 创建测试索引
        result = await test_pdf_processing()
        if not result:
            return False
        
        index_id = result["index_id"]
        
        # 创建服务实例
        embedding_service = EmbeddingService()
        await embedding_service.load_index(index_id)
        
        # 模拟RAG问答流程
        query = "小米有哪些智能家居产品？"
        print(f"用户问题: {query}")
        
        # 1. 检索相关文档
        search_results = await embedding_service.search(query, top_k=3)
        print(f"✓ 检索到 {len(search_results)} 个相关文档片段")
        
        # 2. 构建上下文
        context_parts = []
        for result in search_results:
            context_parts.append(f"文档片段: {result['content']}")
        
        context = "\n\n".join(context_parts)
        print(f"✓ 上下文构建完成，长度: {len(context)} 字符")
        
        # 3. 构建提示词
        prompt = f"""基于以下文档内容回答用户问题：

文档内容：
{context}

用户问题：{query}

请根据文档内容准确回答，如果文档中没有相关信息，请说明。
"""
        
        print("✓ 提示词构建完成")
        print("✓ 传统RAG流程测试成功")
        
        # 显示构建的提示词（截取部分）
        print(f"\n构建的提示词预览:")
        print("-" * 40)
        print(prompt[:300] + "..." if len(prompt) > 300 else prompt)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"✗ 传统RAG问答测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始传统RAG系统测试")
    print("基于FAISS + SentenceTransformers的向量检索系统")
    
    test_results = []
    
    # 1. 基础功能测试
    result1 = await test_embedding_service_basic()
    test_results.append(("EmbeddingService基础功能", result1))
    
    # 2. 文档处理测试
    result2 = await test_pdf_processing()
    test_results.append(("文档处理功能", bool(result2)))
    
    # 3. 向量搜索测试
    result3 = await test_vector_search()
    test_results.append(("向量搜索功能", result3))
    
    # 4. RAG问答测试
    result4 = await test_traditional_rag_chat()
    test_results.append(("RAG问答流程", result4))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("传统RAG系统测试结果总结")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count == len(test_results):
        print("\n🎉 传统RAG系统测试全部通过！")
        print("📋 系统功能:")
        print("  ✓ FAISS向量索引创建和管理")
        print("  ✓ SentenceTransformers文本向量化")
        print("  ✓ 文档解析和分块处理")
        print("  ✓ 向量相似度搜索")
        print("  ✓ RAG问答流程构建")
        print("\n💡 建议:")
        print("  1. 可以集成到main.py中创建传统RAG API端点")
        print("  2. 添加更多文档格式支持（DOCX、TXT等）")
        print("  3. 优化分块策略和搜索算法")
        print("  4. 添加LLM集成进行完整的问答生成")
    else:
        print(f"\n⚠️  {len(test_results) - success_count} 项测试失败")
        print("请检查依赖安装和配置")

if __name__ == "__main__":
    asyncio.run(main())
