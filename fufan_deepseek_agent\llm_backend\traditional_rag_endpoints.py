#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
传统RAG API端点实现
基于FAISS + SentenceTransformers的向量检索系统
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import tempfile
import os
import json
import asyncio
from pathlib import Path

from app.services.embedding_service import EmbeddingService
from app.services.deepseek_service import DeepseekService
from app.core.logger import get_logger

logger = get_logger(service="traditional_rag")

# 创建FastAPI应用
app = FastAPI(title="传统RAG API", description="基于FAISS + SentenceTransformers的向量检索系统")

# 请求模型
class TraditionalRAGRequest(BaseModel):
    query: str
    index_id: str
    top_k: int = 3
    user_id: Optional[int] = None

class TraditionalRAGChatRequest(BaseModel):
    messages: List[Dict[str, str]]
    index_id: str
    top_k: int = 3
    user_id: Optional[int] = None

class IndexSearchRequest(BaseModel):
    query: str
    top_k: int = 3

# 全局服务实例
embedding_service = EmbeddingService()
llm_service = DeepseekService()

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "service": "traditional_rag"}

@app.post("/api/traditional-rag/upload")
async def upload_document(
    file: UploadFile = File(...),
    user_id: int = Form(...)
):
    """上传文档并创建传统RAG索引"""
    try:
        logger.info(f"Processing traditional RAG upload for user {user_id}: {file.filename}")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        try:
            if file.filename.endswith('.pdf'):
                # 处理PDF文件
                result = await embedding_service.create_embeddings(temp_file.name, "traditional_rag")
            else:
                # 处理文本文件
                with open(temp_file.name, 'r', encoding='utf-8') as f:
                    content_text = f.read()
                
                # 简单分块策略
                chunks = content_text.split('\n\n')
                chunks = [chunk.strip() for chunk in chunks if chunk.strip()]
                
                if not chunks:
                    # 如果没有双换行分割，按句子分割
                    chunks = content_text.split('。')
                    chunks = [chunk.strip() + '。' for chunk in chunks if chunk.strip()]
                
                # 创建索引
                index = embedding_service._create_index()
                vectors = embedding_service.model.encode(chunks)
                vectors = vectors.astype('float32')
                index.add(vectors)
                
                # 创建文档数据
                documents = {}
                for i, text in enumerate(chunks):
                    documents[str(i)] = {
                        "text": text,
                        "metadata": {
                            "chunk": i + 1,
                            "source": file.filename,
                            "user_id": user_id
                        }
                    }
                
                # 保存索引
                import hashlib
                file_hash = hashlib.md5(f"{file.filename}_{user_id}".encode()).hexdigest()
                embedding_service._save_index(file_hash, index, documents)
                
                result = {
                    "status": "success",
                    "index_id": f"index_{file_hash}",
                    "chunks": len(chunks)
                }
            
            return {
                "filename": file.filename,
                "user_id": user_id,
                "size": len(content),
                "type": "traditional_rag",
                "index_result": result
            }
            
        finally:
            # 清理临时文件
            os.unlink(temp_file.name)
            
    except Exception as e:
        logger.error(f"Traditional RAG upload failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/traditional-rag/search")
async def search_documents(request: TraditionalRAGRequest):
    """在传统RAG索引中搜索文档"""
    try:
        logger.info(f"Traditional RAG search: {request.query} in index {request.index_id}")
        
        # 加载索引
        await embedding_service.load_index(request.index_id)
        
        # 执行搜索
        results = await embedding_service.search(request.query, top_k=request.top_k)
        
        return {
            "query": request.query,
            "index_id": request.index_id,
            "results": results,
            "total": len(results)
        }
        
    except Exception as e:
        logger.error(f"Traditional RAG search failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/traditional-rag/chat")
async def traditional_rag_chat(request: TraditionalRAGChatRequest):
    """传统RAG问答接口"""
    try:
        logger.info(f"Traditional RAG chat for user {request.user_id}")
        
        # 提取用户问题
        user_question = ""
        for message in reversed(request.messages):
            if message.get("role") == "user":
                user_question = message.get("content", "")
                break
        
        if not user_question:
            raise HTTPException(status_code=400, detail="未找到用户问题")
        
        # 加载索引
        await embedding_service.load_index(request.index_id)
        
        # 检索相关文档
        search_results = await embedding_service.search(user_question, top_k=request.top_k)
        
        # 构建上下文
        context_parts = []
        for i, result in enumerate(search_results, 1):
            context_parts.append(f"文档片段{i}：{result['content']}")
        
        context = "\n\n".join(context_parts)
        
        # 构建提示词
        system_prompt = f"""你是一个智能助手，请基于以下文档内容回答用户问题。

文档内容：
{context}

请根据文档内容准确回答用户问题。如果文档中没有相关信息，请明确说明。"""

        # 构建消息
        rag_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_question}
        ]
        
        # 生成流式响应
        async def generate_response():
            async for chunk in llm_service.generate_stream(rag_messages):
                yield chunk
        
        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream"
        )
        
    except Exception as e:
        logger.error(f"Traditional RAG chat failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/traditional-rag/indexes")
async def list_indexes():
    """列出所有可用的传统RAG索引"""
    try:
        index_dir = embedding_service.index_dir
        indexes = []
        
        for file_path in index_dir.glob("index_*.bin"):
            index_id = file_path.stem
            docs_file = index_dir / f"docs_{index_id[6:]}.json"
            
            if docs_file.exists():
                # 读取文档信息
                with open(docs_file, 'r', encoding='utf-8') as f:
                    docs_data = json.load(f)
                
                # 获取索引信息
                doc_count = len(docs_data)
                first_doc = list(docs_data.values())[0] if docs_data else {}
                source = first_doc.get('metadata', {}).get('source', 'unknown')
                
                indexes.append({
                    "index_id": index_id,
                    "document_count": doc_count,
                    "source": source,
                    "type": "traditional_rag"
                })
        
        return {
            "indexes": indexes,
            "total": len(indexes)
        }
        
    except Exception as e:
        logger.error(f"List indexes failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/traditional-rag/indexes/{index_id}")
async def delete_index(index_id: str):
    """删除传统RAG索引"""
    try:
        index_dir = embedding_service.index_dir
        
        # 删除索引文件
        index_file = index_dir / f"{index_id}.bin"
        docs_file = index_dir / f"docs_{index_id[6:]}.json"
        
        deleted_files = []
        if index_file.exists():
            index_file.unlink()
            deleted_files.append(str(index_file))
        
        if docs_file.exists():
            docs_file.unlink()
            deleted_files.append(str(docs_file))
        
        if not deleted_files:
            raise HTTPException(status_code=404, detail="索引不存在")
        
        return {
            "message": "索引删除成功",
            "deleted_files": deleted_files
        }
        
    except Exception as e:
        logger.error(f"Delete index failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/traditional-rag/compare")
async def compare_with_graphrag():
    """比较传统RAG和GraphRAG的特点"""
    return {
        "traditional_rag": {
            "name": "传统RAG",
            "technology": "FAISS + SentenceTransformers",
            "advantages": [
                "快速向量相似度检索",
                "简单易部署",
                "资源消耗较低",
                "适合文档片段匹配"
            ],
            "use_cases": [
                "快速文档检索",
                "FAQ问答",
                "简单知识查询"
            ]
        },
        "graphrag": {
            "name": "GraphRAG",
            "technology": "知识图谱 + 社区检测",
            "advantages": [
                "复杂推理能力",
                "全局理解",
                "实体关系分析",
                "多层次查询"
            ],
            "use_cases": [
                "复杂知识推理",
                "关系分析",
                "深度问答"
            ]
        },
        "recommendation": "根据具体需求选择：简单查询用传统RAG，复杂推理用GraphRAG"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
