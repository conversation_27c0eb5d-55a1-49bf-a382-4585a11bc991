#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
传统RAG API端点测试
"""

import asyncio
import sys
import os
import json
import tempfile
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_document():
    """创建测试文档"""
    try:
        # 创建临时文本文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w', encoding='utf-8')
        
        content = """智能家居产品目录

第一章：智能音响设备
1. 小米智能音箱Pro
   - 价格：299元
   - 功能：语音控制、音乐播放、智能家居控制
   - 特点：支持小爱同学语音助手

2. 天猫精灵X1
   - 价格：199元
   - 功能：AI语音助手、购物助手
   - 特点：阿里巴巴生态集成

第二章：智能照明系统
1. 飞利浦智能灯泡
   - 价格：89元
   - 功能：可调色温、亮度调节
   - 特点：支持手机APP控制

2. 小米智能台灯
   - 价格：169元
   - 功能：护眼设计、定时开关
   - 特点：无频闪技术

第三章：智能安防产品
1. 海康威视摄像头
   - 价格：399元
   - 功能：1080P高清录像、夜视
   - 特点：云存储支持

2. 德施曼智能门锁
   - 价格：1299元
   - 功能：指纹识别、密码开锁
   - 特点：防撬报警功能
"""
        
        temp_file.write(content)
        temp_path = temp_file.name
        temp_file.close()
        
        print(f"✓ 测试文档创建成功: {temp_path}")
        return temp_path
        
    except Exception as e:
        print(f"✗ 创建测试文档失败: {e}")
        return None

def test_server_health():
    """测试服务器健康状态"""
    print("=" * 60)
    print("测试服务器健康状态")
    print("=" * 60)
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        print("请确保服务器已启动: python run.py")
        return False

def test_file_upload():
    """测试文件上传功能"""
    print("\n" + "=" * 60)
    print("测试文件上传功能")
    print("=" * 60)
    
    try:
        # 创建测试文档
        test_file_path = create_test_document()
        if not test_file_path:
            return None
        
        # 准备上传数据
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_document.txt', f, 'text/plain')}
            data = {'user_id': 1}
            
            print("上传文件到服务器...")
            response = requests.post(
                "http://localhost:8000/api/upload",
                files=files,
                data=data,
                timeout=30
            )
        
        # 清理临时文件
        os.unlink(test_file_path)
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 文件上传成功")
            print(f"  文件名: {result.get('filename')}")
            print(f"  大小: {result.get('size')} 字节")
            print(f"  用户ID: {result.get('user_id')}")
            
            # 检查索引结果
            index_result = result.get('index_result', {})
            if index_result.get('status') == 'success':
                print("✓ 索引创建成功")
                print(f"  索引类型: GraphRAG")
                print(f"  输出目录: {index_result.get('output_dir')}")
            else:
                print("⚠️  索引创建可能失败")
            
            return result
        else:
            print(f"✗ 文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ 文件上传测试失败: {e}")
        return None

def test_traditional_rag_endpoint():
    """测试传统RAG端点（如果存在）"""
    print("\n" + "=" * 60)
    print("测试传统RAG端点")
    print("=" * 60)
    
    # 检查是否有传统RAG端点
    endpoints_to_test = [
        "/api/embedding/search",
        "/api/vector/search", 
        "/api/faiss/search",
        "/api/traditional-rag"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            if response.status_code != 404:
                print(f"✓ 发现端点: {endpoint}")
                return endpoint
        except:
            continue
    
    print("⚠️  未发现传统RAG专用端点")
    print("当前系统主要使用GraphRAG，传统RAG功能需要额外开发")
    return None

def test_graphrag_endpoint():
    """测试GraphRAG端点"""
    print("\n" + "=" * 60)
    print("测试GraphRAG端点")
    print("=" * 60)
    
    try:
        # 测试RAG聊天端点
        test_request = {
            "messages": [
                {"role": "user", "content": "智能音箱有哪些产品？"}
            ],
            "index_id": "default",
            "user_id": 1
        }
        
        print("发送RAG查询请求...")
        response = requests.post(
            "http://localhost:8000/chat-rag",
            json=test_request,
            timeout=30,
            stream=True
        )
        
        if response.status_code == 200:
            print("✓ GraphRAG端点响应正常")
            print("流式响应内容:")
            print("-" * 40)
            
            # 读取流式响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])
                            if 'content' in data:
                                print(data['content'], end='', flush=True)
                            elif 'finished' in data and data['finished']:
                                print("\n✓ 响应完成")
                                break
                        except json.JSONDecodeError:
                            continue
            
            print("-" * 40)
            return True
        else:
            print(f"✗ GraphRAG端点失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ GraphRAG端点测试失败: {e}")
        return False

def test_api_documentation():
    """测试API文档"""
    print("\n" + "=" * 60)
    print("测试API文档")
    print("=" * 60)
    
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✓ API文档可访问")
            print("  访问地址: http://localhost:8000/docs")
            return True
        else:
            print(f"✗ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ API文档测试失败: {e}")
        return False

def analyze_current_rag_architecture():
    """分析当前RAG架构"""
    print("\n" + "=" * 60)
    print("当前RAG架构分析")
    print("=" * 60)
    
    print("📋 发现的RAG实现:")
    print("1. GraphRAG系统 (主要)")
    print("   - 端点: /chat-rag")
    print("   - 技术: 微软GraphRAG + 知识图谱")
    print("   - 状态: ✓ 已实现并集成")
    
    print("\n2. 传统RAG系统 (辅助)")
    print("   - 服务: EmbeddingService")
    print("   - 技术: FAISS + SentenceTransformers")
    print("   - 状态: ⚠️  已实现但未集成到API")
    
    print("\n3. 文件上传系统")
    print("   - 端点: /api/upload")
    print("   - 功能: 触发GraphRAG索引构建")
    print("   - 状态: ✓ 已实现")
    
    print("\n💡 建议:")
    print("1. 当前系统主要基于GraphRAG，功能完整")
    print("2. 传统RAG系统可作为轻量级备选方案")
    print("3. 可以添加传统RAG API端点用于特定场景")

def main():
    """主测试函数"""
    print("🚀 开始传统RAG API测试")
    
    test_results = []
    
    # 1. 服务器健康检查
    result1 = test_server_health()
    test_results.append(("服务器健康检查", result1))
    
    if not result1:
        print("\n❌ 服务器未启动，无法继续测试")
        return
    
    # 2. API文档测试
    result2 = test_api_documentation()
    test_results.append(("API文档访问", result2))
    
    # 3. 文件上传测试
    result3 = test_file_upload()
    test_results.append(("文件上传功能", bool(result3)))
    
    # 4. 传统RAG端点测试
    result4 = test_traditional_rag_endpoint()
    test_results.append(("传统RAG端点", bool(result4)))
    
    # 5. GraphRAG端点测试
    result5 = test_graphrag_endpoint()
    test_results.append(("GraphRAG端点", result5))
    
    # 6. 架构分析
    analyze_current_rag_architecture()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("API测试结果总结")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count >= 3:  # 至少基本功能正常
        print("\n🎉 RAG系统基本功能正常！")
        print("📋 可用功能:")
        print("  ✓ GraphRAG问答 (主要)")
        print("  ✓ 文件上传和索引")
        print("  ✓ API文档访问")
        
        if result4:
            print("  ✓ 传统RAG端点")
        else:
            print("  ⚠️  传统RAG端点需要开发")
    else:
        print("\n⚠️  系统存在问题，需要检查配置")

if __name__ == "__main__":
    main()
